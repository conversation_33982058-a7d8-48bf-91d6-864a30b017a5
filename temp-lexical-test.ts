
import { describe, it, expect } from 'vitest';
import { markdownToLexical } from '../src/markdown';
import { writeFileSync } from 'fs';

describe('Generate Lexical for Ghost Test', () => {
  it('should convert sync-content-test.md to lexical', async () => {
    const markdown = `# Sync Content Test

This is a test post for reproducing the frontmatter cache error when syncing from Ghost post browser.

## Test Content

This post contains basic content to test the sync functionality from Ghost to Obsidian without encountering frontmatter cache errors.

The sync process should handle this content properly even when the local file doesn't exist yet or doesn't have frontmatter in the metadata cache.

\`\`\`javascript
function testSync() {
    console.log("Testing sync functionality");
    return "Sync successful";
}
\`\`\`

## Key Points

- The post should sync successfully from Ghost
- No frontmatter cache errors should occur
- The content should be preserved correctly
- The file should be created in the articles directory

> [!info]
> This is an info callout

**End of test content.**`;

    const result = await markdownToLexical(markdown);
    expect(result.success).toBe(true);

    if (result.success && result.data) {
      // Save the lexical data to a temp file
      writeFileSync('temp-lexical-output.json', JSON.stringify(result.data, null, 2));
    }
  });
});
