import {
  setupE2ETestHooks,
  expectNotice,
  executeCommand,
  expectPostFile
} from '../helpers/shared-context';
import { setupTestFailureHandler } from '../helpers/test-failure-handler';
import { TestPostManager } from '../helpers/ghost-api-helpers';


import { test, expect, describe, beforeEach, afterEach } from 'vitest';

setupTestFailureHandler();

describe("Sync Content Issues", () => {
  const context = setupE2ETestHooks();
  let postManager: TestPostManager;

  beforeEach(async () => {
    postManager = new TestPostManager();
  });

  afterEach(async () => {
    if (postManager) {
      await postManager.cleanup();
    }
  });

  test("should import new posts without using sync path (no frontmatter cache errors)", async () => {
    const testPost = await postManager.createPostFromFixture('sync-content-test.md');

    expect(testPost).toBeTruthy();
    expect(testPost.title).toBe('Sync Content Test');
    expect(testPost.slug).toBe('sync-content-test');

    await context.page.waitForTimeout(2000);

    const fileExistsBefore = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-content-test.md');
      return file !== null;
    });
    expect(fileExistsBefore).toBe(false);

    await executeCommand(context, 'Browse and sync posts from Ghost');
    await context.page.waitForTimeout(3000);

    await context.page.waitForSelector('.ghost-post-suggestion', { timeout: 10000 });
    await context.page.keyboard.type('sync-content-test');
    await context.page.keyboard.press('Enter');

    await expectNotice(context, "Synced", 15000);

    const fileExists = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-content-test.md');
      return file !== null;
    });

    expect(fileExists).toBe(true);

    const fileContent = await context.page.evaluate(() => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath('articles/sync-content-test.md');
      if (file) {
        return app.vault.read(file);
      }
      return null;
    });

    expect(fileContent).toBeTruthy();
    expect(fileContent).toContain('Title: "Sync Content Test"');
    expect(fileContent).toContain('Slug: "sync-content-test"');
    expect(fileContent).toContain('# Sync Content Test');
    expect(fileContent).toContain('```javascript');
    expect(fileContent).toContain('return "Sync successful"');
    expect(fileContent).toContain('> [!info]');
    expect(fileContent).toContain('> This is an info callout');
  });
});
