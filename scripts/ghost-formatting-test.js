#!/usr/bin/env node

/**
 * Clean, simple script to manage ONE formatting test post in Ghost
 * This post contains all the formatting we want to test and serves as our Lexical reference
 */

const GhostAdminAPI = require('@tryghost/admin-api');
const fsPromises = require('fs').promises;
const path = require('path');

// Ghost configuration
const GHOST_CONFIG = {
  url: 'https://obsidian-plugin.ghost.io',
  key: '68a42987d4ff6d0001ea507c:14819197326fe3f39d49d4fe0e060f2c34fb68bb1be94c66c2a2bb252f93f797'
};

// Test post configuration
const TEST_POST = {
  title: 'Content Test',
  slug: 'content-test',
  status: 'draft',
  visibility: 'public'
};

// Function to read and parse the test fixture, converting markdown to HTML
async function getTestContentAsHTML() {
  try {
    const fixturePath = path.join(process.cwd(), 'tests/fixtures/sync-content-test.md');
    const content = await fsPromises.readFile(fixturePath, 'utf8');

    // Extract content without frontmatter using simple parsing
    let markdownContent;
    if (content.startsWith('---\n')) {
      const frontmatterEnd = content.indexOf('\n---\n', 4);
      if (frontmatterEnd !== -1) {
        markdownContent = content.slice(frontmatterEnd + 5).trim();
      } else {
        markdownContent = content.trim();
      }
    } else {
      markdownContent = content.trim();
    }

    // Simple markdown to HTML conversion for basic elements
    let html = markdownContent
      // Headers
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      // Bold and italic
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Code blocks
      .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code>$2</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/g, '<code>$1</code>')
      // Blockquotes
      .replace(/^> \[!info\]\n> (.*$)/gm, '<blockquote><p>ℹ️ $1</p></blockquote>')
      .replace(/^> (.*$)/gm, '<blockquote><p>$1</p></blockquote>')
      // Lists
      .replace(/^- (.*$)/gm, '<li>$1</li>')
      // Paragraphs (simple approach)
      .split('\n\n')
      .map(paragraph => {
        paragraph = paragraph.trim();
        if (!paragraph) return '';
        if (paragraph.startsWith('<h') || paragraph.startsWith('<pre') ||
          paragraph.startsWith('<blockquote') || paragraph.includes('<li>')) {
          return paragraph;
        }
        return `<p>${paragraph}</p>`;
      })
      .join('\n');

    // Wrap list items in ul tags
    html = html.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');

    return html;
  } catch (error) {
    console.warn('Could not read test fixture, using fallback content:', error.message);
    return `<h1>Content Test</h1>
<p>This is a test post for reproducing the frontmatter cache error when syncing from Ghost post browser.</p>
<h2>Test Content</h2>
<p>This post contains basic content to test the sync functionality from Ghost to Obsidian without encountering frontmatter cache errors.</p>
<pre><code>function testSync() {
    console.log("Testing sync functionality");
    return "Sync successful";
}</code></pre>
<p><strong>End of test content.</strong></p>`;
  }
}

class GhostFormattingTest {
  constructor() {
    this.api = new GhostAdminAPI({
      url: GHOST_CONFIG.url,
      key: GHOST_CONFIG.key,
      version: 'v6.0'
    });
  }

  async createOrUpdateTestPost() {
    console.log('🚀 Managing formatting test post in Ghost...');

    try {
      // Check if test post exists
      const existingPosts = await this.api.posts.browse({
        filter: `slug:${TEST_POST.slug}`,
        limit: 1
      });

      let post;

      // Get content from test fixture as HTML
      const htmlContent = await getTestContentAsHTML();

      const postData = {
        ...TEST_POST,
        html: htmlContent,
        mobiledoc: null
      };

      if (existingPosts.length > 0) {
        console.log('📝 Updating existing test post...');
        post = await this.api.posts.edit({
          id: existingPosts[0].id,
          ...postData,
          updated_at: existingPosts[0].updated_at
        });
        console.log('✅ Test post updated');
      } else {
        console.log('📝 Creating new test post...');
        post = await this.api.posts.add(postData);
        console.log('✅ Test post created');
      }

      console.log(`📄 Post ID: ${post.id}`);
      console.log(`🔗 Post URL: ${GHOST_CONFIG.url}/ghost/#/editor/post/${post.id}`);

      return post;
    } catch (error) {
      console.error('❌ Error managing test post:', error);
      throw error;
    }
  }

  async fetchLexicalStructure(onlyLexical = false) {
    console.log('📥 Fetching Lexical structure from Ghost...');

    try {
      const posts = await this.api.posts.browse({
        filter: `slug:${TEST_POST.slug}`,
        limit: 1
      });

      if (posts.length === 0) {
        throw new Error('Test post not found. Run with "create" first.');
      }

      const post = await this.api.posts.read(
        { id: posts[0].id },
        { formats: ['lexical'] }
      );

      console.log('✅ Complete post data fetched');

      if (onlyLexical) {
        // Just dump the lexical JSON to console
        if (post.lexical) {
          console.log('📄 Lexical JSON:');
          console.log(JSON.stringify(JSON.parse(post.lexical), null, 2));
        } else {
          console.log('❌ No lexical content found in post');
        }
        return post;
      }

      // Save complete post JSON to reference file
      const referenceDir = path.join(process.cwd(), 'tests/fixtures/lexical-reference');
      await fsPromises.mkdir(referenceDir, { recursive: true });

      const postFile = path.join(referenceDir, `${TEST_POST.slug}.json`);
      await fsPromises.writeFile(postFile, JSON.stringify(post, null, 2));

      console.log('✅ Complete post reference saved to:', postFile);
      console.log(`📊 Post data size: ${JSON.stringify(post).length} characters`);
      console.log(`📊 Lexical size: ${post.lexical ? post.lexical.length : 0} characters`);

      return post;
    } catch (error) {
      console.error('❌ Error fetching Lexical structure:', error);
      throw error;
    }
  }

  async cleanupOldPosts() {
    console.log('🧹 Cleaning up old test posts...');

    try {
      const posts = await this.api.posts.browse({
        filter: 'slug:obsidian-ghost-sync-test-post,slug:lexical-structure-test',
        limit: 'all'
      });

      for (const post of posts) {
        console.log(`🗑️ Deleting old post: ${post.title}`);
        await this.api.posts.delete({ id: post.id });
      }

      console.log(`✅ Cleaned up ${posts.length} old posts`);
    } catch (error) {
      console.error('❌ Error cleaning up:', error);
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';
  const onlyLexical = args.includes('--only-lexical');
  const manager = new GhostFormattingTest();

  try {
    switch (command) {
      case 'create':
        await manager.createOrUpdateTestPost();
        break;

      case 'fetch':
        await manager.fetchLexicalStructure(onlyLexical);
        break;

      case 'both':
        await manager.createOrUpdateTestPost();
        await manager.fetchLexicalStructure(onlyLexical);
        break;

      case 'cleanup':
        await manager.cleanupOldPosts();
        break;

      default:
        console.log('Usage: node ghost-formatting-test.js [create|fetch|both|cleanup] [--only-lexical]');
        console.log('  create  - Create/update the content test post using fixture content');
        console.log('  fetch   - Fetch Lexical structure from existing post');
        console.log('  both    - Create/update post and fetch structure');
        console.log('  cleanup - Remove old test posts');
        console.log('');
        console.log('Options:');
        console.log('  --only-lexical  - Only dump the lexical JSON (use with fetch or both)');
        console.log('');
        console.log('The script now uses content from tests/fixtures/sync-content-test.md');
        console.log('and creates a post titled "Content Test".');
        break;
    }
  } catch (error) {
    console.error('💥 Failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = GhostFormattingTest;
