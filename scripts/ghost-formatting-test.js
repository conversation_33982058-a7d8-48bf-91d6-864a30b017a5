#!/usr/bin/env node

/**
 * Clean, simple script to manage ONE formatting test post in Ghost
 * This post contains all the formatting we want to test and serves as our Lexical reference
 */

const GhostAdminAPI = require('@tryghost/admin-api');
const fsPromises = require('fs').promises;
const path = require('path');
const matter = require('gray-matter');

// Ghost configuration
const GHOST_CONFIG = {
  url: 'https://obsidian-plugin.ghost.io',
  key: '68a42987d4ff6d0001ea507c:14819197326fe3f39d49d4fe0e060f2c34fb68bb1be94c66c2a2bb252f93f797'
};

// Test post configuration
const TEST_POST = {
  title: 'Content Test',
  slug: 'content-test',
  status: 'draft',
  visibility: 'public'
};

// Function to read and parse the test fixture, converting markdown to Lexical
async function getTestContentAsLexical() {
  try {
    const fixturePath = path.join(process.cwd(), 'tests/fixtures/sync-content-test.md');
    const content = await fsPromises.readFile(fixturePath, 'utf8');

    // Parse frontmatter and content using gray-matter
    const parsed = matter(content);
    const frontMatter = parsed.data;
    const markdownContent = parsed.content;

    // Import the markdown parser - use the wrapper module
    const { markdownToLexical } = await import('./markdown-parser-wrapper.mjs');

    // Convert markdown to Lexical
    const lexicalResult = await markdownToLexical(markdownContent);

    if (!lexicalResult.success || !lexicalResult.data) {
      throw new Error(`Failed to convert markdown to Lexical: ${lexicalResult.error}`);
    }

    return {
      frontMatter,
      lexical: lexicalResult.data,
      markdownContent
    };
  } catch (error) {
    console.warn('Could not read test fixture or convert to Lexical:', error.message);
    // Fallback: create simple Lexical structure
    return {
      frontMatter: {
        title: 'Content Test',
        slug: 'content-test',
        status: 'draft'
      },
      lexical: {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'extended-text',
              text: 'Fallback content - could not parse test fixture',
              format: 0,
              style: '',
              mode: 'normal',
              detail: 0
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      },
      markdownContent: 'Fallback content - could not parse test fixture'
    };
  }
}

class GhostFormattingTest {
  constructor() {
    this.api = new GhostAdminAPI({
      url: GHOST_CONFIG.url,
      key: GHOST_CONFIG.key,
      version: 'v6.0'
    });
  }

  async createTestPost() {
    console.log('🚀 Creating formatting test post in Ghost...');

    try {
      // Check if test post already exists
      const existingPosts = await this.api.posts.browse({
        filter: `slug:${TEST_POST.slug}`,
        limit: 1
      });

      if (existingPosts.length > 0) {
        throw new Error(`Post with slug "${TEST_POST.slug}" already exists. Use 'update' command instead.`);
      }

      // Get content from test fixture as Lexical
      const { frontMatter, lexical } = await getTestContentAsLexical();

      const postData = {
        ...TEST_POST,
        ...frontMatter,
        lexical: JSON.stringify(lexical),
        mobiledoc: null,
        html: null // Let Ghost generate HTML from Lexical
      };

      console.log('📝 Creating new test post...');
      const post = await this.api.posts.add(postData);
      console.log('✅ Test post created');

      console.log(`📄 Post ID: ${post.id}`);
      console.log(`🔗 Post URL: ${GHOST_CONFIG.url}/ghost/#/editor/post/${post.id}`);

      return post;
    } catch (error) {
      console.error('❌ Error creating test post:', error);
      throw error;
    }
  }

  async updateTestPost() {
    console.log('� Updating formatting test post in Ghost...');

    try {
      // Check if test post exists
      const existingPosts = await this.api.posts.browse({
        filter: `slug:${TEST_POST.slug}`,
        limit: 1
      });

      if (existingPosts.length === 0) {
        throw new Error(`Post with slug "${TEST_POST.slug}" not found. Use 'create' command first.`);
      }

      // Get content from test fixture as Lexical
      const { frontMatter, lexical } = await getTestContentAsLexical();

      const postData = {
        ...TEST_POST,
        ...frontMatter,
        lexical: JSON.stringify(lexical),
        mobiledoc: null,
        html: null // Let Ghost generate HTML from Lexical
      };

      console.log('📝 Updating existing test post...');
      const post = await this.api.posts.edit({
        id: existingPosts[0].id,
        ...postData,
        updated_at: existingPosts[0].updated_at
      });
      console.log('✅ Test post updated');

      console.log(`📄 Post ID: ${post.id}`);
      console.log(`🔗 Post URL: ${GHOST_CONFIG.url}/ghost/#/editor/post/${post.id}`);

      return post;
    } catch (error) {
      console.error('❌ Error updating test post:', error);
      throw error;
    }
  }

  async fetchLexicalStructure(onlyLexical = false) {
    console.log('📥 Fetching Lexical structure from Ghost...');

    try {
      const posts = await this.api.posts.browse({
        filter: `slug:${TEST_POST.slug}`,
        limit: 1
      });

      if (posts.length === 0) {
        throw new Error('Test post not found. Run with "create" first.');
      }

      const post = await this.api.posts.read(
        { id: posts[0].id },
        { formats: ['lexical'] }
      );

      console.log('✅ Complete post data fetched');

      if (onlyLexical) {
        // Save lexical JSON to fixture file
        if (post.lexical) {
          const lexicalData = JSON.parse(post.lexical);
          const fixtureFile = path.join(process.cwd(), 'tests/fixtures/sync-content-test.lexical.json');
          await fsPromises.writeFile(fixtureFile, JSON.stringify(lexicalData, null, 2));

          console.log('📄 Lexical JSON saved to:', fixtureFile);
          console.log(`📊 Lexical size: ${post.lexical.length} characters`);
        } else {
          console.log('❌ No lexical content found in post');
        }
        return post;
      }

      // Save complete post JSON to reference file
      const referenceDir = path.join(process.cwd(), 'tests/fixtures/lexical-reference');
      await fsPromises.mkdir(referenceDir, { recursive: true });

      const postFile = path.join(referenceDir, `${TEST_POST.slug}.json`);
      await fsPromises.writeFile(postFile, JSON.stringify(post, null, 2));

      console.log('✅ Complete post reference saved to:', postFile);
      console.log(`📊 Post data size: ${JSON.stringify(post).length} characters`);
      console.log(`📊 Lexical size: ${post.lexical ? post.lexical.length : 0} characters`);

      return post;
    } catch (error) {
      console.error('❌ Error fetching Lexical structure:', error);
      throw error;
    }
  }

  async cleanupOldPosts() {
    console.log('🧹 Cleaning up old test posts...');

    try {
      const posts = await this.api.posts.browse({
        filter: 'slug:obsidian-ghost-sync-test-post,slug:lexical-structure-test',
        limit: 'all'
      });

      for (const post of posts) {
        console.log(`🗑️ Deleting old post: ${post.title}`);
        await this.api.posts.delete({ id: post.id });
      }

      console.log(`✅ Cleaned up ${posts.length} old posts`);
    } catch (error) {
      console.error('❌ Error cleaning up:', error);
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';
  const onlyLexical = args.includes('--only-lexical');
  const manager = new GhostFormattingTest();

  try {
    switch (command) {
      case 'create':
        await manager.createTestPost();
        break;

      case 'update':
        await manager.updateTestPost();
        break;

      case 'fetch':
        await manager.fetchLexicalStructure(onlyLexical);
        break;

      case 'cleanup':
        await manager.cleanupOldPosts();
        break;

      default:
        console.log('Usage: node ghost-formatting-test.js [create|update|fetch|cleanup] [--only-lexical]');
        console.log('  create  - Create the content test post using fixture content');
        console.log('  update  - Update existing content test post using fixture content');
        console.log('  fetch   - Fetch Lexical structure from existing post');
        console.log('  cleanup - Remove old test posts');
        console.log('');
        console.log('Options:');
        console.log('  --only-lexical  - Save lexical JSON to tests/fixtures/sync-content-test.lexical.json (use with fetch)');
        console.log('');
        console.log('The script uses content from tests/fixtures/sync-content-test.md');
        console.log('and converts it to Lexical format using our markdown parser.');
        break;
    }
  } catch (error) {
    console.error('💥 Failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = GhostFormattingTest;
