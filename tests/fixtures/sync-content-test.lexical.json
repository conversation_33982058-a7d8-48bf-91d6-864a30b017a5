{"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "Sync Content Test", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "extended-heading", "version": 1, "tag": "h1"}, {"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "This is a test post for reproducing the frontmatter cache error when syncing from Ghost post browser.", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1}, {"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "Test Content", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "extended-heading", "version": 1, "tag": "h2"}, {"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "This post contains basic content to test the sync functionality from Ghost to Obsidian without encountering frontmatter cache errors.", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1}, {"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "The sync process should handle this content properly even when the local file doesn't exist yet or doesn't have frontmatter in the metadata cache.", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1}, {"type": "codeblock", "version": 1, "code": "function testSync() {\n    console.log(\"Testing sync functionality\");\n    return \"Sync successful\";\n}", "language": "javascript", "caption": ""}, {"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "Key Points", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "extended-heading", "version": 1, "tag": "h2"}, {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "The post should sync successfully from <PERSON>", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "listitem", "version": 1, "value": 1}, {"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "No frontmatter cache errors should occur", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "listitem", "version": 1, "value": 2}, {"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "The content should be preserved correctly", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "listitem", "version": 1, "value": 3}, {"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "The file should be created in the articles directory", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "listitem", "version": 1, "value": 4}], "direction": "ltr", "format": "", "indent": 0, "type": "list", "version": 1, "listType": "bullet", "start": 1, "tag": "ul"}, {"type": "callout", "version": 1, "calloutText": "<p dir=\"ltr\"><span style=\"white-space: pre-wrap;\">This is an info callout</span></p>", "calloutEmoji": "ℹ️", "backgroundColor": "blue"}, {"children": [{"detail": 0, "format": 1, "mode": "normal", "style": "", "text": "End of test content.", "type": "extended-text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}