/**
 * Tests for the Obsidian-Ghost Markdown parser
 */

import { describe, it, expect } from 'vitest';
import {
  Markdown,
  markdownToLexical,
  lexicalToMarkdown,
  roundTrip,
} from '../../src/markdown';

describe('Obsidian-Ghost Markdown Parser', () => {
  describe('Basic Setup', () => {
    it('should create a Markdown parser instance', () => {
      const parser = new Markdown();
      expect(parser).toBeDefined();
      parser.destroy();
    });

    it('should handle invalid input gracefully', async () => {
      const result = await markdownToLexical(null as any);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Input cannot be null or undefined');
    });

    it('should handle empty string input', async () => {
      const result = await markdownToLexical('');
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should test direct Lexical conversion', async () => {
      const { createHeadlessEditor } = await import('@lexical/headless');
      const { $convertFromMarkdownString, $convertToMarkdownString, TRANSFORMERS } = await import('@lexical/markdown');
      const { ParagraphNode, TextNode } = await import('lexical');
      const { HeadingNode, QuoteNode } = await import('@lexical/rich-text');
      const { ListItemNode, ListNode } = await import('@lexical/list');
      const { CodeNode } = await import('@lexical/code');
      const { LinkNode } = await import('@lexical/link');

      const editor = createHeadlessEditor({
        namespace: 'test',
        nodes: [
          ParagraphNode,
          TextNode,
          HeadingNode,
          ListNode,
          ListItemNode,
          QuoteNode,
          CodeNode,
          LinkNode,
        ],
      });

      const markdown = 'This is a simple paragraph.';

      editor.update(() => {
        $convertFromMarkdownString(markdown, TRANSFORMERS);
      }, {
        discrete: true,
      });

      const state = editor.getEditorState().toJSON();
      console.log('Direct Lexical state:', JSON.stringify(state, null, 2));

      const result = editor.getEditorState().read(() => {
        return $convertToMarkdownString(TRANSFORMERS);
      });

      console.log('Direct Lexical markdown result:', result);
      expect(result).toBeTruthy();
    });
  });

  describe('Plain Text Paragraph', () => {
    it('should convert plain text to Lexical and back', async () => {
      const markdown = 'This is a simple paragraph.';

      // Convert to Lexical
      const lexicalResult = await markdownToLexical(markdown);
      expect(lexicalResult.success).toBe(true);
      expect(lexicalResult.data).toBeDefined();
      expect(lexicalResult.data?.root).toBeDefined();

      // Convert back to markdown
      const markdownResult = await lexicalToMarkdown(lexicalResult.data!);

      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBeDefined();

      // The result should be the same or equivalent
      expect(markdownResult.data?.trim()).toBe(markdown);
    });

    it('should handle multiple paragraphs', async () => {
      const markdown = `First paragraph.

Second paragraph.`;

      const result = await roundTrip(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      // Should preserve paragraph structure
      const lines = result.data!.split('\n');
      expect(lines.length).toBeGreaterThan(1);
    });

    it('should preserve whitespace in paragraphs', async () => {
      const markdown = 'This is a paragraph with  multiple  spaces.';

      const result = await roundTrip(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
    });

    it('should handle paragraphs with special characters', async () => {
      const markdown = 'This paragraph has special chars: @#$%^&*()';

      const result = await roundTrip(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      // Lexical correctly escapes special markdown characters like *
      const expectedMarkdown = 'This paragraph has special chars: @#$%^&\\*()';
      expect(result.data?.trim()).toBe(expectedMarkdown);
    });

    it('should handle unicode characters', async () => {
      const markdown = 'Unicode test: 🚀 こんにちは 你好';

      const result = await roundTrip(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.trim()).toBe(markdown);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed Lexical document', async () => {
      const malformedDoc = {
        root: null as any,
        nodes: [] as any[],
      };

      const result = await lexicalToMarkdown(malformedDoc);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid document structure');
    });

    it('should handle missing document', async () => {
      const result = await lexicalToMarkdown(null as any);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Document cannot be null or undefined');
    });
  });

  describe('Parser Instance Management', () => {
    it('should allow multiple parser instances', async () => {
      const parser1 = new Markdown();
      const parser2 = new Markdown();

      const result1 = await parser1.markdownToLexical('Test 1');
      const result2 = await parser2.markdownToLexical('Test 2');

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);

      parser1.destroy();
      parser2.destroy();
    });

    it('should handle concurrent conversions', async () => {
      const texts = [
        'First text',
        'Second text',
        'Third text',
      ];

      const promises = texts.map(text => markdownToLexical(text));
      const results = await Promise.all(promises);

      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.data).toBeDefined();
      });
    });
  });

  describe('Obsidian Italic Formatting', () => {
    it('should handle asterisk italic syntax', async () => {
      const markdown = 'This is *italic* text.';

      const result = await roundTrip(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.trim()).toBe(markdown);
    });

    it('should handle underscore italic syntax', async () => {
      const markdown = 'This is _italic_ text.';

      const result = await roundTrip(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      // Lexical normalizes underscore italic to asterisk italic
      const expectedMarkdown = 'This is *italic* text.';
      expect(result.data?.trim()).toBe(expectedMarkdown);
    });

    it('should handle mixed italic syntax', async () => {
      const markdown = 'This has *asterisk* and _underscore_ italic.';

      const result = await roundTrip(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      // Note: Lexical may normalize to one format
      expect(result.data?.trim()).toContain('italic');
    });

    it('should handle italic at start and end of text', async () => {
      const markdown = '*Start italic* and end *italic*';

      const result = await roundTrip(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.trim()).toContain('italic');
    });

    it('should handle nested formatting with italic', async () => {
      const markdown = 'This is **bold and *italic* text**.';

      const result = await roundTrip(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.trim()).toContain('bold');
      expect(result.data?.trim()).toContain('italic');
    });

    it('should not treat underscores in words as italic', async () => {
      const markdown = 'This has snake_case_variable names.';

      const result = await roundTrip(markdown);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();

      // Lexical correctly escapes underscores in words to prevent italic interpretation
      const expectedMarkdown = 'This has snake\\_case\\_variable names.';
      expect(result.data?.trim()).toBe(expectedMarkdown);
    });
  });

  describe('Ghost codeblock conversion', () => {
    it('should convert Ghost codeblock nodes to Lexical code nodes', async () => {
      // This is the actual structure from Ghost's lexical output
      const ghostCodeblockDocument = {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "Hello World",
                  type: "extended-text",
                  version: 1
                }
              ],
              direction: "ltr",
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1
            },
            {
              type: "codeblock",
              version: 1,
              code: "IO.puts \"Hello\"",
              language: "elixir",
              caption: ""
            },
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: "normal",
                  style: "",
                  text: "That's it.",
                  type: "extended-text",
                  version: 1
                }
              ],
              direction: "ltr",
              format: "",
              indent: 0,
              type: "paragraph",
              version: 1
            }
          ],
          direction: "ltr",
          format: "",
          indent: 0,
          type: "root",
          version: 1
        }
      };

      const result = await lexicalToMarkdown(ghostCodeblockDocument);

      expect(result.success).toBe(true);
      expect(result.data).toContain('Hello World');
      expect(result.data).toContain('```elixir');
      expect(result.data).toContain('IO.puts "Hello"');
      expect(result.data).toContain('```');
      expect(result.data).toContain("That's it.");

      // Verify the complete structure
      const lines = result.data!.trim().split('\n');
      expect(lines).toContain('Hello World');
      expect(lines).toContain('```elixir');
      expect(lines).toContain('IO.puts "Hello"');
      expect(lines).toContain('```');
      expect(lines).toContain("That's it.");
    });

    it('should handle codeblock without language', async () => {
      const codeblockWithoutLanguage = {
        root: {
          children: [
            {
              type: "codeblock",
              version: 1,
              code: "console.log('hello')",
              language: "",
              caption: ""
            }
          ],
          direction: "ltr",
          format: "",
          indent: 0,
          type: "root",
          version: 1
        }
      };

      const result = await lexicalToMarkdown(codeblockWithoutLanguage);

      expect(result.success).toBe(true);
      expect(result.data).toContain('```');
      expect(result.data).toContain("console.log('hello')");
    });

    it('should handle empty codeblock', async () => {
      const emptyCodeblock = {
        root: {
          children: [
            {
              type: "codeblock",
              version: 1,
              code: "",
              language: "javascript",
              caption: ""
            }
          ],
          direction: "ltr",
          format: "",
          indent: 0,
          type: "root",
          version: 1
        }
      };

      const result = await lexicalToMarkdown(emptyCodeblock);

      expect(result.success).toBe(true);
      expect(result.data).toContain('```javascript');
      expect(result.data).toContain('```');
    });
  });
});
